// src/components/layout/app-layout.tsx
'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuth } from '../auth/auth-provider';
import AppContent from './app-content';
import AppFooter from './app-footer';
import AppHeader from './app-header';
import AppSidebar from './app-sidebar';

interface AppLayoutProps {
  children: React.ReactNode;
  title?: string;
  showFooter?: boolean;
}

const AppLayout = ({ children, title, showFooter = false }: AppLayoutProps) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { user, loading } = useAuth();

  // Get current route for breadcrumb
  const getCurrentRoute = () => {
    const route = pathname.split('/')[1];
    return route || 'dashboard';
  };

  // Get page title based on route
  const getPageTitle = () => {
    if (title) return title;
    
    const routeTitles: Record<string, string> = {
      'dashboard': 'Dashboard',
      'student-management': 'Student Management',
      'staff-management': 'Staff Management',
      'academic-management': 'Academic Management',
      'attendance-management': 'Attendance Management',
      'fee-management': 'Fee Management',
    };

    const route = getCurrentRoute();
    return routeTitles[route] || 'Dashboard';
  };

  useEffect(() => {
    if (!loading && !user) {
      router.push('/product');
    }
  }, [user, loading, router]);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // useEffect will handle the redirect
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Sidebar - Fixed position */}
      <div className="flex-shrink-0 transition-all duration-300 relative">
        <AppSidebar
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={toggleSidebar}
        />
      </div>

      {/* Main Content Area with proper margin for fixed sidebar */}
      <div className={`flex flex-col min-h-screen transition-all duration-300 ${
        isSidebarCollapsed ? 'ml-16' : 'ml-72'
      }`}>
        {/* Elegant separation line */}
        <div className={`absolute top-0 bottom-0 w-0.5 bg-gradient-to-b from-slate-600 via-slate-500 to-slate-600 z-10 transition-all duration-300 ${
          isSidebarCollapsed ? 'left-16' : 'left-72'
        }`}></div>
        
        {/* Header - Fixed at top with consistent height */}
        <div className="flex-shrink-0">
          <AppHeader 
            title={getPageTitle()}
            currentRoute={getCurrentRoute()}
          />
        </div>

        {/* Page Content - Scrollable */}
        <AppContent>
          {children}
        </AppContent>

        {/* Footer - Optional */}
        {showFooter && (
          <div className="flex-shrink-0">
            <AppFooter />
          </div>
        )}
      </div>
    </div>
  );
};

export default AppLayout;
