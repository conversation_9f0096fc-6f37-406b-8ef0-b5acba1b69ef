// src/app/dashboard/_components/dashboard-content.tsx
'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useAuth } from '../../../components/auth/auth-provider';
import PageWrapper from '../../../components/common/page-wrapper';

const DashboardContent = () => {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Only redirect if auth is fully loaded and user is definitely not authenticated
    if (!loading && (!user || !user.isAuthenticated)) {
      // Give a small delay to avoid immediate redirect on page load
      const timer = setTimeout(() => {
        router.push('/auth');
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user || !user.isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-pulse">
            <p className="text-slate-600">Redirecting to login...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PageWrapper>
      {/* Exciting News Banner */}
      <div className="bg-gradient-to-r from-indigo-600 to-blue-600 rounded-xl p-4 mb-4 text-white shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-white/20 rounded-lg p-2">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-base-app font-semibold">Version 2.0 is Live!</h3>
              <p className="text-blue-100 text-base-app opacity-90">
                Enhanced features, improved performance, and refreshed UI.
              </p>
            </div>
          </div>
          <button className="bg-white text-indigo-600 px-4 py-2 rounded-lg text-base font-semibold hover:bg-blue-50 transition-colors shadow-sm">
            Explore
          </button>
        </div>
      </div>

      {/* Quick Action Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 mb-4">
        {/* Create Project */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-2 shadow-sm border border-blue-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-6 h-6 bg-blue-500/20 rounded-lg flex items-center justify-center mb-1.5">
            <svg className="w-3 h-3 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <h3 className="font-semibold text-blue-900 mb-1 text-sm-app">Create Project</h3>
          <button className="text-blue-700 text-sm-app font-medium hover:text-blue-800 hover:underline transition-all">
            Start →
          </button>
        </div>

        {/* Upload Docs */}
        <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg p-2 shadow-sm border border-green-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-6 h-6 bg-green-500/20 rounded-lg flex items-center justify-center mb-1.5">
            <svg className="w-3 h-3 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          <h3 className="font-semibold text-green-900 mb-1 text-sm">Upload Docs</h3>
          <button className="text-green-700 text-sm font-medium hover:text-green-800 hover:underline transition-all">
            Upload →
          </button>
        </div>

        {/* View Analytics */}
        <div className="bg-gradient-to-br from-orange-50 to-amber-100 rounded-lg p-2 shadow-sm border border-orange-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-6 h-6 bg-orange-500/20 rounded-lg flex items-center justify-center mb-1.5">
            <svg className="w-3 h-3 text-orange-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="font-semibold text-orange-900 mb-1 text-sm-app">Analytics</h3>
          <button className="text-orange-700 text-sm-app font-medium hover:text-orange-800 hover:underline transition-all">
            View →
          </button>
        </div>

        {/* Manage Users */}
        <div className="bg-gradient-to-br from-pink-50 to-rose-100 rounded-lg p-3 shadow-sm border border-pink-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-8 h-8 bg-pink-500/20 rounded-lg flex items-center justify-center mb-2">
            <svg className="w-4 h-4 text-pink-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <h3 className="font-semibold text-pink-900 mb-1 text-sm-app">Users</h3>
          <button className="text-pink-700 text-sm-app font-medium hover:text-pink-800 hover:underline transition-all">
            Manage →
          </button>
        </div>

        {/* Reports */}
        <div className="bg-gradient-to-br from-purple-50 to-violet-100 rounded-lg p-3 shadow-sm border border-purple-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center mb-2">
            <svg className="w-4 h-4 text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="font-semibold text-purple-900 mb-1 text-sm-app">Reports</h3>
          <button className="text-purple-700 text-sm-app font-medium hover:text-purple-800 hover:underline transition-all">
            Generate →
          </button>
        </div>

        {/* Settings */}
        <div className="bg-gradient-to-br from-gray-50 to-slate-100 rounded-lg p-3 shadow-sm border border-gray-200/50 hover:shadow-md transition-all duration-300">
          <div className="w-8 h-8 bg-gray-500/20 rounded-lg flex items-center justify-center mb-2">
            <svg className="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <h3 className="font-semibold text-gray-900 mb-1 text-sm">Settings</h3>
          <button className="text-gray-700 text-sm font-medium hover:text-gray-800 hover:underline transition-all">
            Configure →
          </button>
        </div>
      </div>

      {/* Performance Overview */}
      <div className="mb-6">
        <h2 className="section-header-md">Performance Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* User Engagement */}
          <div className="card-lg hover:shadow-xl transition-all duration-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="card-title-sm">User Engagement</h3>
              <div className="text-blue-600 bg-blue-50 p-2 rounded-lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <div className="text-3xl-app font-bold text-gray-900 mb-2">+15%</div>
            <div className="text-sm-app text-gray-600">Last 30 Days</div>
          </div>

          {/* Product Sales */}
          <div className="card-lg hover:shadow-xl transition-all duration-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="card-title-sm">Product Sales</h3>
              <div className="text-green-600 bg-green-50 p-2 rounded-lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
            </div>
            <div className="text-3xl-app font-bold text-gray-900 mb-2">+8%</div>
            <div className="text-sm-app text-gray-600">Last Quarter</div>
          </div>

          {/* Server Uptime */}
          <div className="bg-white rounded-xl p-5 shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-900 text-base">Server Uptime</h3>
              <div className="text-purple-600 bg-purple-50 p-2 rounded-lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2" />
                </svg>
              </div>
            </div>
            <div className="text-3xl font-bold text-gray-900 mb-2">99.98%</div>
            <div className="text-sm text-gray-600">Last 7 Days</div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div>
        <h2 className="text-lg font-bold text-gray-900 mb-4">Recent Activity</h2>
        <div className="bg-white rounded-xl shadow-lg border border-gray-200/50">
          <div className="p-8">
            <div className="text-center text-gray-500">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <p className="text-base font-semibold text-gray-700 mb-2">No recent activity</p>
              <p className="text-sm text-gray-500">Activity will appear here as you use the system</p>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default DashboardContent;
