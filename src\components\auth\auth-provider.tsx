// src/components/auth/auth-provider.tsx
'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '../../lib/auth';
import { SupabaseAuthService } from '../../lib/supabase-auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, userData: { name: string; role: string }) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>;
  signInWithFacebook: () => Promise<{ success: boolean; error?: string }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Initialize auth state
    const initializeAuth = async () => {
      try {
        const currentUser = await SupabaseAuthService.getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error('Failed to initialize auth:', error);
      } finally {
        setLoading(false);
      }
    };

    // Set a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.warn('Auth initialization timeout - forcing loading to false');
      setLoading(false);
    }, 10000); // 10 second timeout

    initializeAuth().then(() => {
      clearTimeout(timeoutId);
    });

    // Listen for auth changes
    const { data: { subscription } } = SupabaseAuthService.onAuthStateChange((user) => {
      setUser(user);
      setLoading(false);
    });

    return () => {
      clearTimeout(timeoutId);
      subscription?.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    const result = await SupabaseAuthService.signIn(email, password);
    if (result.success && result.user) {
      setUser(result.user);
    }
    return { success: result.success, error: result.error || undefined };
  };

  const signUp = async (email: string, password: string, userData: { name: string; role: string }) => {
    const result = await SupabaseAuthService.signUp(email, password, userData);
    if (result.success && result.user) {
      setUser(result.user);
    }
    return { success: result.success, error: result.error || undefined };
  };

  const signOut = async () => {
    await SupabaseAuthService.signOut();
    setUser(null);
  };

  const signInWithGoogle = async () => {
    const result = await SupabaseAuthService.signInWithGoogle();
    return { success: result.success, error: result.error || undefined };
  };

  const signInWithFacebook = async () => {
    const result = await SupabaseAuthService.signInWithFacebook();
    return { success: result.success, error: result.error || undefined };
  };

  const value: AuthContextType = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    signInWithFacebook,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
