/*
===============================================================================
                    STUDENT WIZARD COMPONENT STYLES
===============================================================================

This file contains styles specific to the Add Student Wizard component.
It inherits from the global design system and ensures consistent typography
and styling across all wizard steps.

DESIGN PRINCIPLES:
- Inherit typography from globals.css design system
- Consistent form element styling across all steps
- Professional appearance with proper spacing
- Responsive design for mobile and desktop

===============================================================================
*/

/* Import global design system */
@import '../../../globals.css';

/* Wizard Container Styles */
.student-wizard {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.student-wizard::before {
  content: '';
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: -1;
}

/* Compact Modal Container */
.wizard-modal {
  background: white;
  border-radius: 16px;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transform: scale(1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 800px; /* Reduced from 1000px for more compact design */
  width: 90vw; /* Reduced from 95vw for better centering */
}

.wizard-modal:hover {
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Compact Wizard Header Styles */
.wizard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  position: relative;
  overflow: hidden;
  padding: 1rem 1.5rem 0.75rem 1.5rem;
}

.wizard-header::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 50%, rgba(240, 147, 251, 0.9) 100%);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.wizard-header > * {
  position: relative;
  z-index: 1;
}

.wizard-title {
  @apply text-xl-app font-bold text-white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 0.5rem;
}

.wizard-step-indicator {
  @apply text-xs-app font-semibold text-white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.wizard-step-description {
  display: none; /* Hide descriptions to save space */
}

/* Form Element Styles - Consistent Typography */
.wizard-form-label {
  @apply text-xs-app font-semibold text-gray-800 mb-1 block;
  font-family: inherit;
}

.wizard-form-label.required::after {
  content: " *";
  @apply text-red-500;
}

.wizard-form-input {
  @apply w-full px-3 py-2 border border-gray-200/80 rounded-lg text-sm-app font-medium placeholder:text-gray-400 transition-all duration-300;
  font-family: inherit;
  height: 2.25rem; /* 36px - compact height */
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-form-input:hover {
  border-color: rgba(156, 163, 175, 0.6);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-form-input:focus {
  @apply ring-2 ring-indigo-500/20 border-indigo-500/60 outline-none;
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 8px 25px rgba(79, 70, 229, 0.15),
    0 0 0 4px rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.wizard-form-input.error {
  @apply border-red-300/80 focus:ring-red-500/20 focus:border-red-500/60;
  background: rgba(254, 242, 242, 0.8);
}

.wizard-form-select {
  @apply w-full px-3 py-2 border border-gray-200/80 rounded-lg text-sm-app font-medium transition-all duration-300 appearance-none;
  font-family: inherit;
  height: 2.25rem; /* 36px - compact height */
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  padding-right: 2.5rem;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-form-select:hover {
  border-color: rgba(156, 163, 175, 0.6);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-form-select:focus {
  @apply ring-2 ring-indigo-500/20 border-indigo-500/60 outline-none;
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 8px 25px rgba(79, 70, 229, 0.15),
    0 0 0 4px rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.wizard-form-textarea {
  @apply w-full px-3 py-2 border border-gray-200/80 rounded-lg resize-none text-sm-app font-medium placeholder:text-gray-400 transition-all duration-300;
  font-family: inherit;
  min-height: 3.5rem; /* 56px - compact minimum height */
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-form-textarea:hover {
  border-color: rgba(156, 163, 175, 0.6);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-form-textarea:focus {
  @apply ring-2 ring-indigo-500/20 border-indigo-500/60 outline-none;
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 8px 25px rgba(79, 70, 229, 0.15),
    0 0 0 4px rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

/* Date Input Specific Styles */
.wizard-form-date {
  @apply wizard-form-input text-sm-app;
}

/* Error Message Styles */
.wizard-form-error {
  @apply text-sm-app text-red-600 mt-1 flex items-center;
  font-family: inherit;
}

/* Modern Button Styles */
.wizard-btn {
  @apply font-semibold rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 relative overflow-hidden;
  font-family: inherit;
  position: relative;
  transform: translateY(0);
}

.wizard-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.wizard-btn:hover::before {
  opacity: 1;
}

.wizard-btn-primary {
  @apply wizard-btn px-6 py-2 text-white text-sm-app;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 35px rgba(102, 126, 234, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.wizard-btn-secondary {
  @apply wizard-btn px-4 py-2 text-gray-700 text-sm-app;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(209, 213, 219, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-btn-secondary:hover {
  transform: translateY(-1px);
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(156, 163, 175, 0.8);
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-btn-success {
  @apply wizard-btn px-6 py-2 text-white text-sm-app;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow:
    0 8px 25px rgba(16, 185, 129, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-btn-success:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 35px rgba(16, 185, 129, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.wizard-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
  transform: translateY(0) !important;
  box-shadow: none !important;
}

.wizard-btn:disabled::before {
  opacity: 0 !important;
}

/* Step Content Styles */
.wizard-step-content {
  @apply space-y-6;
}

.wizard-form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4;
}

.wizard-form-grid.three-cols {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.wizard-form-group {
  @apply space-y-1;
}

.wizard-form-group.full-width {
  @apply md:col-span-2 lg:col-span-2;
}

.wizard-form-group.full-width-three {
  @apply md:col-span-2 lg:col-span-3;
}

/* Centered form content for better space utilization */
.wizard-step-content {
  @apply w-full max-w-none mx-auto;
}

/* Optimized spacing for form sections */
.wizard-form-section {
  @apply mb-5;
}

.wizard-form-section:last-child {
  @apply mb-0;
}

/* Compact File Upload Styles */
.wizard-file-upload {
  @apply border-2 border-dashed rounded-lg p-3 text-center cursor-pointer transition-all duration-300 relative overflow-hidden;
  border-color: rgba(209, 213, 219, 0.6);
  background: rgba(249, 250, 251, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-file-upload::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, rgba(79, 70, 229, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.wizard-file-upload:hover {
  border-color: rgba(79, 70, 229, 0.4);
  background: rgba(79, 70, 229, 0.05);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-file-upload:hover::before {
  opacity: 1;
}

.wizard-file-upload-icon {
  @apply w-8 h-8 rounded-lg flex items-center justify-center mx-auto mb-2 relative z-10 transition-all duration-300;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(79, 70, 229, 0.05));
  box-shadow:
    0 4px 12px rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wizard-file-upload:hover .wizard-file-upload-icon {
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.2), rgba(79, 70, 229, 0.1));
  transform: scale(1.1);
  box-shadow:
    0 6px 16px rgba(79, 70, 229, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.wizard-file-upload-text {
  @apply text-sm-app font-semibold text-gray-700 relative z-10;
  font-family: inherit;
}

.wizard-file-upload-subtext {
  @apply text-xs-app text-gray-500 font-medium relative z-10;
  font-family: inherit;
}

/* Compact Horizontal Step Progress Styles */
.wizard-step-progress {
  @apply flex items-center justify-center mt-3 px-4;
  gap: 0.5rem;
}

.wizard-step-item {
  @apply flex items-center;
  position: relative;
}

.wizard-step-circle {
  @apply w-8 h-8 rounded-full flex items-center justify-center font-bold text-xs relative z-10 transition-all duration-300;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.wizard-step-circle.active {
  @apply bg-white text-indigo-600;
  box-shadow:
    0 4px 12px rgba(79, 70, 229, 0.3),
    0 0 0 2px rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.wizard-step-circle.completed {
  @apply bg-gradient-to-br from-green-500 to-emerald-600 text-white;
  box-shadow:
    0 4px 12px rgba(34, 197, 94, 0.3),
    0 0 0 2px rgba(34, 197, 94, 0.1);
  transform: scale(1.05);
}

.wizard-step-circle.inactive {
  @apply bg-white/30 text-white/70 backdrop-blur-sm;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.wizard-step-content {
  @apply ml-2;
  max-width: 80px;
}

.wizard-step-line {
  @apply h-0.5 bg-gradient-to-r flex-1 mx-2 relative;
  min-width: 20px;
  max-width: 40px;
}

.wizard-step-line.completed {
  @apply from-white to-white;
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
}

.wizard-step-line.incomplete {
  @apply from-white/30 to-white/30;
}

.wizard-step-line::before {
  content: '';
  position: absolute;
  inset: 0;
  background: inherit;
  border-radius: 2px;
  filter: blur(1px);
}

/* Step Icons for Completed Steps */
.wizard-step-circle.completed .step-number {
  display: none;
}

.wizard-step-circle.completed::after {
  content: '✓';
  font-size: 12px;
  font-weight: bold;
}

/* Compact Content Container */
.wizard-content {
  @apply p-5 overflow-y-auto max-h-[65vh];
  background: linear-gradient(to bottom, rgba(249, 250, 251, 0.5), rgba(255, 255, 255, 0.8));
  position: relative;
}

.wizard-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(79, 70, 229, 0.2), transparent);
}

/* Compact Footer */
.wizard-footer {
  @apply border-t border-gray-100 p-3;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(249, 250, 251, 0.9));
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  position: relative;
}

.wizard-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(79, 70, 229, 0.1), transparent);
}

.wizard-footer-actions {
  @apply flex items-center justify-between;
  position: relative;
  z-index: 1;
}

/* Loading States */
.wizard-loading {
  @apply animate-spin rounded-full h-4 w-4 border-b-2 border-white;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .wizard-modal {
    margin: 1rem;
    max-height: 95vh;
  }

  .wizard-header {
    padding: 1rem;
  }

  .wizard-title {
    @apply text-xl-app;
  }

  .wizard-step-progress {
    @apply mt-4 px-2;
    gap: 0.5rem;
  }

  .wizard-step-circle {
    @apply w-10 h-10 text-xs-app;
  }

  .wizard-step-content {
    @apply hidden;
  }

  .wizard-step-line {
    min-width: 20px;
    max-width: 40px;
  }

  .wizard-content {
    @apply p-4;
  }

  .wizard-form-grid,
  .wizard-form-grid.three-cols {
    @apply grid-cols-1 gap-3;
  }

  .wizard-form-input,
  .wizard-form-select,
  .wizard-form-textarea {
    @apply text-xs-app;
    height: 2rem; /* 32px - smaller on mobile */
  }

  .wizard-form-textarea {
    min-height: 3rem; /* 48px - smaller on mobile */
  }

  .wizard-btn-primary,
  .wizard-btn-success {
    @apply px-4 py-2 text-xs-app;
  }

  .wizard-btn-secondary {
    @apply px-3 py-2 text-xs-app;
  }

  .wizard-footer {
    @apply p-4;
  }

  .wizard-footer-actions {
    @apply flex-col gap-3;
  }

  .wizard-footer-actions > * {
    @apply w-full;
  }
}

@media (min-width: 769px) and (max-width: 1023px) {
  .wizard-form-grid {
    @apply grid-cols-2 gap-4;
  }
}

/* Focus and Accessibility */
.wizard-form-input:focus,
.wizard-form-select:focus,
.wizard-form-textarea:focus {
  @apply outline-none;
}

/* Validation States */
.wizard-form-input.valid {
  @apply border-green-300 focus:ring-green-500/20 focus:border-green-500 bg-green-50/50;
}

.wizard-form-select.valid {
  @apply border-green-300 focus:ring-green-500/20 focus:border-green-500 bg-green-50/50;
}

/* Modern Animation Classes */
.wizard-fade-in {
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.wizard-modal {
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.wizard-step-content {
  animation: stepContentFadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes stepContentFadeIn {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse animation for active step */
@keyframes pulse {
  0%, 100% {
    box-shadow:
      0 8px 25px rgba(79, 70, 229, 0.3),
      0 0 0 4px rgba(79, 70, 229, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 8px 25px rgba(79, 70, 229, 0.4),
      0 0 0 6px rgba(79, 70, 229, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

.wizard-step-circle.active {
  animation: pulse 2s infinite;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: transform, box-shadow, background-color, border-color, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
