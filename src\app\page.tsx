'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuth } from '../components/auth/auth-provider';

export default function HomePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const { user, loading } = useAuth();  useEffect(() => {
    // Wait for auth provider to finish loading
    if (loading) {
      return;
    }

    const checkAuthAndRedirect = () => {
      if (user && user.isAuthenticated) {
        // User is authenticated, redirect to dashboard
        router.push('/dashboard');
      } else {
        // User is not authenticated, redirect to product page
        router.push('/product');
      }

      setIsLoading(false);
    };

    checkAuthAndRedirect();
  }, [router, user, loading]);
  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white text-slate-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading EduPro...</p>
        </div>
      </div>
    );
  }

  return null;
}
