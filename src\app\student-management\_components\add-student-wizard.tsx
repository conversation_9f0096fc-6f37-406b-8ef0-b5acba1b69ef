// src/app/student-management/_components/add-student-wizard.tsx
'use client';

import React, { useEffect, useState } from 'react';
import { useMasterData } from '../../../hooks/useMasterData';
import { useStudents } from '../../../hooks/useStudents';
import { MasterDataService } from '../../../services/masterDataService';
import { CreateStudentData } from '../../../services/studentManagementService';
import './student-wizard.css';

interface AddStudentWizardProps {
  onClose: () => void;
  onSuccess?: (message: string) => void;
  onError?: (error: string) => void;
}

const AddStudentWizard = ({ onClose, onSuccess, onError }: AddStudentWizardProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rollNumberError, setRollNumberError] = useState<string | null>(null);

  // Get master data
  const { data: masterData, loading: masterDataLoading, error: masterDataError } = useMasterData();
  const { createStudent } = useStudents();

  const [formData, setFormData] = useState({
    // Personal Info
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    gender: '',
    email: '',
    phoneNumber: '',
    address: '',
    
    // Guardian Details
    guardianName: '',
    guardianRelationId: '',
    guardianPhone: '',
    guardianEmail: '',
    guardianAddress: '',
    emergencyContact: '',
    
    // Academic Info
    classId: '',
    sectionId: '',
    rollNumber: '',
    previousSchool: '',
    academicYearId: '',
    
    // Documents (File objects for upload)
    birthCertificate: null as File | null,
    previousRecords: null as File | null,
    medicalRecords: null as File | null,
    photograph: null as File | null
  });  // Auto-select current academic year
  useEffect(() => {
    const currentYear = masterData?.currentAcademicYear;
    if (currentYear && !formData.academicYearId) {
      setFormData(prev => ({
        ...prev,
        academicYearId: currentYear.id
      }));
    }
  }, [masterData, formData.academicYearId]);

  const steps = [
    { number: 1, title: 'Personal', description: '' },
    { number: 2, title: 'Guardian', description: '' },
    { number: 3, title: 'Academic', description: '' },
    { number: 4, title: 'Documents', description: '' }
  ];

  const handleInputChange = (field: string, value: string | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear roll number error when relevant fields change
    if (['rollNumber', 'classId', 'sectionId', 'academicYearId'].includes(field)) {
      setRollNumberError(null);
    }
  };

  // Check roll number availability
  const checkRollNumber = async () => {
    if (!formData.rollNumber || !formData.classId || !formData.sectionId || !formData.academicYearId) {
      return true;
    }

    try {
      const exists = await MasterDataService.isRollNumberExists(
        formData.rollNumber,
        formData.classId,
        formData.sectionId,
        formData.academicYearId
      );

      if (exists) {
        setRollNumberError('This roll number already exists for the selected class, section, and academic year');
        return false;
      }

      setRollNumberError(null);
      return true;
    } catch (error) {
      console.error('Error checking roll number:', error);
      setRollNumberError('Error checking roll number availability');
      return false;
    }
  };

  const validateStep = async (step: number): Promise<boolean> => {
    console.log('🔍 Validating step:', step, 'Form data:', formData);

    switch (step) {
      case 1:
        const step1Valid = !!(formData.firstName && formData.lastName && formData.dateOfBirth && formData.gender);
        console.log('Step 1 validation:', {
          firstName: !!formData.firstName,
          lastName: !!formData.lastName,
          dateOfBirth: !!formData.dateOfBirth,
          gender: !!formData.gender,
          isValid: step1Valid
        });
        return step1Valid;
      case 2:
        const step2Valid = !!(formData.guardianName && formData.guardianRelationId && formData.guardianPhone);
        console.log('Step 2 validation:', {
          guardianName: !!formData.guardianName,
          guardianRelationId: !!formData.guardianRelationId,
          guardianPhone: !!formData.guardianPhone,
          isValid: step2Valid
        });
        return step2Valid;
      case 3:
        const rollNumberValid = await checkRollNumber();
        const step3Valid = !!(formData.classId && formData.sectionId && formData.rollNumber && formData.academicYearId) && rollNumberValid;
        console.log('Step 3 validation:', {
          classId: !!formData.classId,
          sectionId: !!formData.sectionId,
          rollNumber: !!formData.rollNumber,
          academicYearId: !!formData.academicYearId,
          rollNumberValid,
          isValid: step3Valid
        });
        return step3Valid;
      case 4:
        return true; // Documents are optional
      default:
        return false;
    }
  };

  const handleNext = async () => {
    console.log('🚀 Next button clicked, current step:', currentStep);
    try {
      const isValid = await validateStep(currentStep);
      console.log('✅ Validation result:', isValid);

      if (isValid && currentStep < 4) {
        console.log('✅ Moving to next step:', currentStep + 1);
        setCurrentStep(currentStep + 1);
      } else {
        console.log('❌ Validation failed or already at last step');
        if (!isValid) {
          // Show validation errors to user
          alert('Please fill in all required fields before proceeding.');
        }
      }
    } catch (error) {
      console.error('❌ Error during validation:', error);
      alert('An error occurred during validation. Please try again.');
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    console.log('🚀 Submit button clicked');

    try {
      const isValid = await validateStep(currentStep);
      if (!isValid) {
        console.log('❌ Final validation failed');
        return;
      }
    } catch (error) {
      console.error('❌ Error during final validation:', error);
      return;
    }

    setIsSubmitting(true);
    try {
      console.log('📝 Preparing student data for submission...');

      // TODO: Upload files to Supabase Storage and get URLs
      // For now, we'll submit without file URLs
      const studentData: CreateStudentData = {
        student: {
          first_name: formData.firstName,
          last_name: formData.lastName,
          email: formData.email || null,
          phone_number: formData.phoneNumber || null,
          date_of_birth: formData.dateOfBirth,
          gender: formData.gender as 'male' | 'female' | 'other',
          address: formData.address || null,
          emergency_contact: formData.emergencyContact || null,
          guardian_name: formData.guardianName,
          guardian_relation_id: formData.guardianRelationId,
          guardian_phone: formData.guardianPhone,
          guardian_email: formData.guardianEmail || null,
          guardian_address: formData.guardianAddress || null,
          class_id: formData.classId,
          section_id: formData.sectionId,
          roll_number: formData.rollNumber,
          academic_year_id: formData.academicYearId,
          previous_school: formData.previousSchool || null,
          is_active: true,
          // File URLs will be added after upload implementation
          birth_certificate_url: null,
          previous_records_url: null,
          medical_records_url: null,
          photograph_url: null
        }
      };

      console.log('📤 Submitting student data:', {
        name: `${studentData.student.first_name} ${studentData.student.last_name}`,
        rollNumber: studentData.student.roll_number,
        classId: studentData.student.class_id,
        sectionId: studentData.student.section_id,
        academicYearId: studentData.student.academic_year_id
      });

      const result = await createStudent(studentData);
      console.log('✅ Student created successfully:', {
        studentId: result.student.id,
        name: `${result.student.first_name} ${result.student.last_name}`,
        rollNumber: result.academicRecord.roll_number
      });

      onSuccess?.(`Student ${result.student.first_name} ${result.student.last_name} created successfully!`);
      onClose();
    } catch (error) {
      console.error('❌ Error creating student:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create student';
      onError?.(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (field: string, file: File | null) => {
    setFormData(prev => ({ ...prev, [field]: file }));
  };

  if (masterDataLoading) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white rounded-2xl p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <span className="ml-3 text-gray-700">Loading master data...</span>
          </div>
        </div>
      </div>
    );
  }

  if (masterDataError) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white rounded-2xl p-8 max-w-md">
          <div className="text-center">
            <div className="text-red-600 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 19c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Data</h3>
            <p className="text-gray-600 mb-4">{masterDataError}</p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:        return (
          <div className="wizard-step-content">
            <div className="wizard-form-section">
              <div className="wizard-form-grid three-cols">
                <div className="wizard-form-group">
                  <label className="wizard-form-label required">First Name</label>
                  <input
                    type="text"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    placeholder="Enter first name"
                    className="wizard-form-input"
                    required
                  />
                </div>

                <div className="wizard-form-group">
                  <label className="wizard-form-label required">Last Name</label>
                  <input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    placeholder="Enter last name"
                    className="wizard-form-input"
                    required
                  />
                </div>

                <div className="wizard-form-group">
                  <label className="wizard-form-label required">Date of Birth</label>
                  <input
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                    className="wizard-form-date"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="wizard-form-section">
              <div className="wizard-form-grid three-cols">
                <div className="wizard-form-group">
                  <label className="wizard-form-label required">Gender</label>
                  <select
                    value={formData.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    className="wizard-form-select"
                    required
                  >
                    <option value="">Select Gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div className="wizard-form-group">
                  <label className="wizard-form-label">Email Address</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Enter email address"
                    className="wizard-form-input"
                  />
                </div>

                <div className="wizard-form-group">
                  <label className="wizard-form-label">Phone Number</label>
                  <input
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                    placeholder="Enter phone number"
                    className="wizard-form-input"
                  />
                </div>
              </div>
            </div>

            <div className="wizard-form-section">
              <div className="wizard-form-group full-width-three">
                <label className="wizard-form-label">Address</label>
                <textarea
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Enter current address"
                  rows={3}
                  className="wizard-form-textarea"
                />
              </div>
            </div>
          </div>
        );      case 2:
        return (
          <div className="wizard-step-content">
            <div className="wizard-form-section">
              <div className="wizard-form-grid three-cols">
                <div className="wizard-form-group">
                  <label className="wizard-form-label required">Guardian Name</label>
                  <input
                    type="text"
                    value={formData.guardianName}
                    onChange={(e) => handleInputChange('guardianName', e.target.value)}
                    placeholder="Enter guardian name"
                    className="wizard-form-input"
                    required
                  />
                </div>

                <div className="wizard-form-group">
                  <label className="wizard-form-label required">Relation</label>
                  <select
                    value={formData.guardianRelationId}
                    onChange={(e) => handleInputChange('guardianRelationId', e.target.value)}
                    className="wizard-form-select"
                    required
                  >
                    <option value="">Select Relation</option>
                    {masterData?.guardianRelations.map((relation) => (
                      <option key={relation.id} value={relation.id}>
                        {relation.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="wizard-form-group">
                  <label className="wizard-form-label required">Guardian Phone</label>
                  <input
                    type="tel"
                    value={formData.guardianPhone}
                    onChange={(e) => handleInputChange('guardianPhone', e.target.value)}
                    placeholder="Enter guardian phone"
                    className="wizard-form-input"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="wizard-form-section">
              <div className="wizard-form-grid">
                <div className="wizard-form-group">
                  <label className="wizard-form-label">Guardian Email</label>
                  <input
                    type="email"
                    value={formData.guardianEmail}
                    onChange={(e) => handleInputChange('guardianEmail', e.target.value)}
                    placeholder="Enter guardian email"
                    className="wizard-form-input"
                  />
                </div>

                <div className="wizard-form-group">
                  <label className="wizard-form-label">Emergency Contact</label>
                  <input
                    type="tel"
                    value={formData.emergencyContact}
                    onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                    placeholder="Enter emergency contact number"
                    className="wizard-form-input"
                  />
                </div>
              </div>
            </div>

            <div className="wizard-form-section">
              <div className="wizard-form-group full-width">
                <label className="wizard-form-label">Guardian Address</label>
                <textarea
                  value={formData.guardianAddress}
                  onChange={(e) => handleInputChange('guardianAddress', e.target.value)}
                  placeholder="Enter guardian address"
                  rows={3}
                  className="wizard-form-textarea"
                />
              </div>
            </div>
          </div>
        );      case 3:
        return (
          <div className="wizard-step-content">
            <div className="wizard-form-section">
              <div className="wizard-form-grid three-cols">
                <div className="wizard-form-group">
                  <label className="wizard-form-label required">Class</label>
                  <select
                    value={formData.classId}
                    onChange={(e) => handleInputChange('classId', e.target.value)}
                    className="wizard-form-select"
                    required
                  >
                    <option value="">Select Class</option>
                    {masterData?.classes.map((cls) => (
                      <option key={cls.id} value={cls.id}>
                        {cls.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="wizard-form-group">
                  <label className="wizard-form-label required">Section</label>
                  <select
                    value={formData.sectionId}
                    onChange={(e) => handleInputChange('sectionId', e.target.value)}
                    className="wizard-form-select"
                    required
                  >
                    <option value="">Select Section</option>
                    {masterData?.sections.map((section) => (
                      <option key={section.id} value={section.id}>
                        {section.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="wizard-form-group">
                  <label className="wizard-form-label required">Roll Number</label>
                  <input
                    type="text"
                    value={formData.rollNumber}
                    onChange={(e) => handleInputChange('rollNumber', e.target.value)}
                    placeholder="Enter roll number"
                    className={`wizard-form-input ${rollNumberError ? 'error' : ''}`}
                    required
                  />
                  {rollNumberError && (
                    <p className="wizard-form-error">{rollNumberError}</p>
                  )}
                </div>
              </div>
            </div>

            <div className="wizard-form-section">
              <div className="wizard-form-grid">
                <div className="wizard-form-group">
                  <label className="wizard-form-label required">Academic Year</label>
                  <select
                    value={formData.academicYearId}
                    onChange={(e) => handleInputChange('academicYearId', e.target.value)}
                    className="wizard-form-select"
                    required
                  >
                    <option value="">Select Academic Year</option>
                    {masterData?.academicYears.map((year) => (
                      <option key={year.id} value={year.id}>
                        {year.year}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="wizard-form-group">
                  <label className="wizard-form-label">Previous School</label>
                  <input
                    type="text"
                    value={formData.previousSchool}
                    onChange={(e) => handleInputChange('previousSchool', e.target.value)}
                    placeholder="Enter previous school name"
                    className="wizard-form-input"
                  />
                </div>
              </div>
            </div>
          </div>
        );      case 4:
        return (
          <div className="wizard-step-content">
            <div className="text-center mb-6">
              <p className="text-gray-600">Upload required documents (all documents are optional but recommended)</p>
            </div>

            <div className="wizard-form-section">
              <div className="wizard-form-grid">
                <FileUploadField
                  label="Birth Certificate"
                  file={formData.birthCertificate}
                  onChange={(file) => handleFileChange('birthCertificate', file)}
                  required={false}
                />

                <FileUploadField
                  label="Previous Records"
                  file={formData.previousRecords}
                  onChange={(file) => handleFileChange('previousRecords', file)}
                  required={false}
                />
              </div>
            </div>

            <div className="wizard-form-section">
              <div className="wizard-form-grid">
                <FileUploadField
                  label="Medical Records"
                  file={formData.medicalRecords}
                  onChange={(file) => handleFileChange('medicalRecords', file)}
                  required={false}
                />

                <FileUploadField
                  label="Photograph"
                  file={formData.photograph}
                  onChange={(file) => handleFileChange('photograph', file)}
                  required={false}
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 p-4 student-wizard">
      <div className="wizard-modal w-full max-h-[90vh] overflow-hidden">        {/* Header */}
        <div className="wizard-header text-white">
          <div className="flex items-center justify-between mb-2">
            <div className="w-8"></div> {/* Spacer for centering */}
            <h2 className="wizard-title text-center flex-1">Add New Student</h2>
            <button
              onClick={onClose}
              className="text-white/80 hover:text-white transition-all duration-200 p-2 rounded-lg hover:bg-white/10"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Modern Horizontal Step indicators */}
          <div className="wizard-step-progress">
            {steps.map((step, index) => (
              <React.Fragment key={step.number}>
                <div className="wizard-step-item">
                  <div className={`wizard-step-circle ${
                    currentStep > step.number ? 'completed' :
                    currentStep === step.number ? 'active' : 'inactive'
                  }`}>
                    <span className="step-number">{step.number}</span>
                  </div>
                  <div className="wizard-step-content">
                    <p className="wizard-step-indicator">{step.title}</p>
                    <p className="wizard-step-description">{step.description}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`wizard-step-line ${
                    currentStep > step.number ? 'completed' : 'incomplete'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="wizard-content">
          {renderStepContent()}
        </div>

        {/* Footer */}
        <div className="wizard-footer">
          <div className="wizard-footer-actions">
            <button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="wizard-btn-secondary"
            >
              Previous
            </button>

            <div className="flex items-center space-x-3">
              {currentStep < 4 ? (
                <button
                  onClick={handleNext}
                  className="wizard-btn-primary"
                >
                  Next
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="wizard-btn-success"
                >
                  {isSubmitting ? (
                    <span className="flex items-center">
                      <div className="wizard-loading mr-2"></div>
                      Creating...
                    </span>
                  ) : (
                    'Create Student'
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// File upload component
interface FileUploadFieldProps {
  label: string;
  file: File | null;
  onChange: (file: File | null) => void;
  required?: boolean;
}

const FileUploadField = ({ label, file, onChange, required = false }: FileUploadFieldProps) => {
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0] || null;
    onChange(selectedFile);
  };

  return (
    <div className="wizard-form-group">
      <label className={`wizard-form-label ${required ? 'required' : ''}`}>
        {label}
      </label>
      <div className="wizard-file-upload">
        <input
          type="file"
          className="sr-only"
          accept=".pdf,.jpg,.jpeg,.png"
          onChange={handleFileSelect}
          id={`file-${label.replace(/\s+/g, '-').toLowerCase()}`}
        />
        <label
          htmlFor={`file-${label.replace(/\s+/g, '-').toLowerCase()}`}
          className="cursor-pointer"
        >
          <div className="flex flex-col items-center space-y-3">
            <div className="wizard-file-upload-icon">
              <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </div>
            {file ? (
              <div className="text-center">
                <p className="wizard-file-upload-text text-green-600">{file.name}</p>
                <p className="wizard-file-upload-subtext">Click to change</p>
              </div>
            ) : (
              <>
                <span className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors shadow-md">
                  Choose File
                </span>
                <p className="wizard-file-upload-subtext">PDF, JPG, PNG up to 10MB</p>
              </>
            )}
          </div>
        </label>
      </div>
    </div>
  );
};

export default AddStudentWizard;
